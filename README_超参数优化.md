# ASB-DPTAM-BiGRU模型超参数优化指南

## 📋 概述

本文档提供了ASB-DPTAM-BiGRU模型的超参数优化方案，包括两种不同的优化方法：

1. **完整优化方案** (`hyperparameter_optimization_asb_dptam_bigru.py`) - 基于Optuna的高级优化
2. **快速搜索方案** (`quick_hyperparameter_search.py`) - 适合快速测试的简化版本

## 🎯 优化的超参数

### 模型结构参数
- **n_segment**: DPTAM分段数 (4-12)
- **dptam_kernel_size**: DPTAM卷积核大小 (3,5,7)
- **bigru_units**: BiGRU层单元数 [第一层, 第二层]
- **dense_units**: 全连接层单元数 [第一层, 第二层]
- **dropout_rate**: Dropout比例 (0.1-0.5)
- **asb_adaptive_filter**: ASB自适应滤波开关

### 训练参数
- **learning_rate**: 学习率 (1e-4 到 1e-2)
- **batch_size**: 批次大小 (16, 32, 64, 128)

## 🚀 使用方法

### 方法一：完整优化方案 (推荐)

```bash
python hyperparameter_optimization_asb_dptam_bigru.py
```

**特点：**
- 使用Optuna TPE算法进行智能搜索
- 支持早停和剪枝，节省计算资源
- 自动生成可视化结果
- 训练最佳参数的完整模型

**配置选项：**
```python
optimizer = ASBDPTAMBiGRUHyperparameterOptimizer(
    n_trials=100,    # 优化试验次数
    timeout=3600     # 超时时间(秒)，None表示无限制
)
```

### 方法二：快速搜索方案

```bash
python quick_hyperparameter_search.py
```

**特点：**
- 支持随机搜索和网格搜索
- 训练epochs较少，适合快速测试
- 计算资源需求较低

**配置选项：**
```python
searcher = QuickHyperparameterSearch(
    search_method='random',  # 'random' 或 'grid'
    n_trials=20             # 搜索次数
)
```

## 📊 结果输出

### 完整优化方案输出
```
results/hyperparameter_optimization/
├── best_hyperparameters.json      # 最佳参数配置
├── optuna_study.pkl               # 完整的Optuna研究对象
├── best_asb_dptam_bigru_model/    # 最佳模型文件
└── visualizations/                # 可视化结果
    ├── optimization_history.png   # 优化历史
    ├── param_importances.png      # 参数重要性
    ├── parallel_coordinate.png    # 并行坐标图
    └── correlation_heatmap.png    # 相关性热图
```

### 快速搜索方案输出
```
results/quick_hyperparameter_search/
└── random_search_results.json     # 搜索结果
```

## 🔧 自定义配置

### 修改搜索空间

在 `define_hyperparameter_space()` 方法中修改参数范围：

```python
def define_hyperparameter_space(self, trial):
    # 自定义参数范围
    n_segment = trial.suggest_int('n_segment', 4, 12, step=2)
    dptam_kernel_size = trial.suggest_int('dptam_kernel_size', 3, 7, step=2)
    # ... 其他参数
```

### 修改优化目标

默认优化目标是验证集RMSE，可以在 `objective_function()` 中修改：

```python
# 可选的优化目标
val_rmse = results['val']['RMSE']      # 均方根误差 (默认)
val_mae = results['val']['MAE']        # 平均绝对误差
val_r2 = -results['val']['R2']         # R²决定系数 (注意取负值)
val_mape = results['val']['MAPE']      # 平均绝对百分比误差
```

## 📈 优化策略建议

### 1. 分阶段优化
```python
# 第一阶段：粗搜索 (快速找到大致范围)
searcher = QuickHyperparameterSearch(search_method='random', n_trials=50)

# 第二阶段：精搜索 (在最佳范围内细化)
optimizer = ASBDPTAMBiGRUHyperparameterOptimizer(n_trials=200)
```

### 2. 计算资源分配
- **高性能服务器**: 使用完整优化方案，n_trials=200-500
- **个人电脑**: 使用快速搜索方案，n_trials=20-50
- **GPU集群**: 可以并行运行多个优化任务

### 3. 参数优先级
根据经验，参数重要性排序：
1. **learning_rate** - 对收敛速度影响最大
2. **bigru_units** - 影响模型容量
3. **dropout_rate** - 影响过拟合
4. **n_segment** - 影响时序建模精度
5. **batch_size** - 影响训练稳定性

## 🎯 最佳实践

### 1. 数据预处理
确保数据质量：
```python
# 检查数据
print(f"数据形状: {df.shape}")
print(f"缺失值: {df.isnull().sum().sum()}")
print(f"数据范围: {df.describe()}")
```

### 2. 早停策略
```python
# 在objective_function中使用早停
if trial.should_prune():
    raise optuna.TrialPruned()
```

### 3. 结果验证
```python
# 使用最佳参数重新训练并验证
best_model = train_with_best_params(best_params)
final_results = evaluate_model(best_model)
```

## 🔍 故障排除

### 常见问题

1. **内存不足**
   - 减少batch_size
   - 减少模型复杂度
   - 使用梯度累积

2. **序列长度不匹配**
   - 确保sequence_length能被n_segment整除
   - 调整数据预处理参数

3. **优化收敛慢**
   - 增加n_trials
   - 调整搜索空间范围
   - 使用更好的初始参数

### 调试技巧

```python
# 启用详细日志
import logging
optuna.logging.set_verbosity(optuna.logging.DEBUG)

# 保存中间结果
trial.set_user_attr('intermediate_results', results)
```

## 📚 参考资料

- [Optuna官方文档](https://optuna.readthedocs.io/)
- [超参数优化最佳实践](https://arxiv.org/abs/1803.09820)
- [贝叶斯优化原理](https://arxiv.org/abs/1012.2599)

## 🤝 贡献

如果您有改进建议或发现问题，请：
1. 提交Issue描述问题
2. 提供复现步骤
3. 建议解决方案

---

**注意**: 超参数优化是一个计算密集型任务，建议在有足够计算资源的环境中运行。首次使用建议先运行快速搜索方案进行测试。
