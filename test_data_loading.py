"""
测试数据加载脚本
验证Wind farm site 2数据集是否能正确加载
"""

import os
import sys

# 添加项目根目录到系统路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.data_processing.data_loader import DataLoader
from src.data_processing.preprocessor import DataPreprocessor
from src.utils.config import (
    MODEL_CONFIG, DATASET_CONFIG, setup_matplotlib,
    setup_training_session, get_current_paths
)


def test_data_loading():
    """测试数据加载功能"""
    print("=" * 80)
    print("测试Wind farm site 2数据集加载")
    print("=" * 80)
    
    # 设置训练会话
    setup_training_session('Data_Loading_Test')
    
    try:
        # 1. 测试数据加载
        print("\n步骤1: 测试数据加载")
        print("-" * 40)
        
        data_loader = DataLoader(data_file=DATASET_CONFIG['data_file'])
        df = data_loader.load_data()
        
        print(f"✅ 数据加载成功！")
        print(f"数据形状: {df.shape}")
        print(f"数据列: {list(df.columns)}")
        print(f"目标列: {DATASET_CONFIG['target_column']}")
        print(f"时间列: {DATASET_CONFIG['datetime_column']}")
        
        # 检查目标列是否存在
        if DATASET_CONFIG['target_column'] in df.columns:
            print(f"✅ 目标列 '{DATASET_CONFIG['target_column']}' 存在")
            print(f"目标列统计: {df[DATASET_CONFIG['target_column']].describe()}")
        else:
            print(f"❌ 目标列 '{DATASET_CONFIG['target_column']}' 不存在")
            print(f"可用列: {list(df.columns)}")
        
        # 检查时间列是否存在
        if DATASET_CONFIG['datetime_column'] in df.columns:
            print(f"✅ 时间列 '{DATASET_CONFIG['datetime_column']}' 存在")
        else:
            print(f"❌ 时间列 '{DATASET_CONFIG['datetime_column']}' 不存在")
            print(f"可用列: {list(df.columns)}")
        
        # 2. 测试数据预处理
        print("\n步骤2: 测试数据预处理")
        print("-" * 40)

        preprocessor = DataPreprocessor()
        preprocessor.set_data(df)
        df_processed = preprocessor.preprocess()
        feature_columns = preprocessor.select_features()

        # 准备训练数据
        processed_data = preprocessor.prepare_data_for_training(
            sequence_length=MODEL_CONFIG['sequence_length'],
            train_ratio=MODEL_CONFIG['train_ratio'],
            val_ratio=MODEL_CONFIG['val_ratio']
        )
        
        print(f"✅ 数据预处理成功！")
        print(f"序列长度: {processed_data['sequence_length']}")
        print(f"特征数量: {processed_data['n_features']}")
        print(f"训练集形状: X_train={processed_data['X_train'].shape}, y_train={processed_data['y_train'].shape}")
        print(f"验证集形状: X_val={processed_data['X_val'].shape}, y_val={processed_data['y_val'].shape}")
        print(f"测试集形状: X_test={processed_data['X_test'].shape}, y_test={processed_data['y_test'].shape}")
        
        # 3. 测试数据加载器创建
        print("\n步骤3: 测试数据加载器创建")
        print("-" * 40)

        train_loader, val_loader, test_loader = preprocessor.create_pytorch_dataloaders(
            processed_data,
            batch_size=MODEL_CONFIG['batch_size']
        )
        
        print(f"✅ 数据加载器创建成功！")
        print(f"训练批次数: {len(train_loader)}")
        print(f"验证批次数: {len(val_loader)}")
        print(f"测试批次数: {len(test_loader)}")
        print(f"批次大小: {MODEL_CONFIG['batch_size']}")
        
        # 4. 测试一个批次的数据
        print("\n步骤4: 测试批次数据")
        print("-" * 40)
        
        for batch_X, batch_y in train_loader:
            print(f"✅ 批次数据获取成功！")
            print(f"批次X形状: {batch_X.shape}")
            print(f"批次y形状: {batch_y.shape}")
            print(f"X数据类型: {batch_X.dtype}")
            print(f"y数据类型: {batch_y.dtype}")
            break
        
        # 5. 检查序列长度与分段数的兼容性
        print("\n步骤5: 检查超参数兼容性")
        print("-" * 40)
        
        sequence_length = processed_data['sequence_length']
        test_segments = [4, 6, 8, 12]
        
        print(f"序列长度: {sequence_length}")
        print("测试分段数兼容性:")
        for n_segment in test_segments:
            if sequence_length % n_segment == 0:
                print(f"  ✅ n_segment={n_segment}: 兼容 (段长度={sequence_length//n_segment})")
            else:
                print(f"  ❌ n_segment={n_segment}: 不兼容")
        
        print("\n" + "=" * 80)
        print("✅ 数据加载测试完成！所有功能正常")
        print("=" * 80)
        
        return True
        
    except Exception as e:
        print(f"\n❌ 数据加载测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主函数"""
    success = test_data_loading()
    
    if success:
        print("\n🎉 数据加载测试通过！可以开始超参数优化")
        print("\n📋 下一步操作:")
        print("1. 运行快速搜索: python quick_hyperparameter_search.py")
        print("2. 运行完整优化: python hyperparameter_optimization_asb_dptam_bigru.py")
    else:
        print("\n⚠️ 数据加载测试失败！请检查数据文件和配置")


if __name__ == "__main__":
    main()
