"""
增强版训练进度显示器
提供详细的训练进度信息，包括进度条、时间估计、损失值等
"""

import time
import sys
from typing import Dict, Optional
from datetime import datetime, timedelta
import torch
from tqdm import tqdm


class EnhancedTrainingProgress:
    """增强版训练进度显示器"""
    
    def __init__(self, total_epochs: int, train_batches: int, val_batches: int):
        """
        初始化进度显示器
        
        Args:
            total_epochs: 总训练轮数
            train_batches: 训练批次数
            val_batches: 验证批次数
        """
        self.total_epochs = total_epochs
        self.train_batches = train_batches
        self.val_batches = val_batches
        self.start_time = None
        self.epoch_start_time = None
        self.best_val_loss = float('inf')
        self.no_improve_count = 0
        
    def start_training(self):
        """开始训练"""
        self.start_time = time.time()
        print("=" * 80)
        print("🚀 开始模型训练")
        print("=" * 80)
        print(f"总训练轮数: {self.total_epochs}")
        print(f"训练批次数: {self.train_batches}")
        print(f"验证批次数: {self.val_batches}")
        print(f"开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("=" * 80)
    
    def start_epoch(self, epoch: int):
        """开始新的训练轮次"""
        self.epoch_start_time = time.time()
        self.current_epoch = epoch
        
    def create_train_progress_bar(self, epoch: int) -> tqdm:
        """创建训练阶段的进度条"""
        return tqdm(
            total=self.train_batches,
            desc=f"Epoch {epoch+1:2d}/{self.total_epochs} [Train]",
            ncols=120,
            bar_format='{l_bar}{bar}| {n_fmt}/{total_fmt} [{elapsed}<{remaining}, {rate_fmt}]',
            leave=False
        )
    
    def create_val_progress_bar(self, epoch: int) -> tqdm:
        """创建验证阶段的进度条"""
        return tqdm(
            total=self.val_batches,
            desc=f"Epoch {epoch+1:2d}/{self.total_epochs} [Val]  ",
            ncols=120,
            bar_format='{l_bar}{bar}| {n_fmt}/{total_fmt} [{elapsed}<{remaining}, {rate_fmt}]',
            leave=False
        )
    
    def update_train_progress(self, pbar: tqdm, batch_idx: int, loss: float, mae: float):
        """更新训练进度"""
        pbar.set_postfix({
            'loss': f'{loss:.4f}',
            'mae': f'{mae:.4f}'
        })
        pbar.update(1)
    
    def update_val_progress(self, pbar: tqdm, batch_idx: int, loss: float, mae: float):
        """更新验证进度"""
        pbar.set_postfix({
            'loss': f'{loss:.4f}',
            'mae': f'{mae:.4f}'
        })
        pbar.update(1)
    
    def print_epoch_summary(self, epoch: int, train_loss: float, train_mae: float, 
                          val_loss: float, val_mae: float, learning_rate: float):
        """打印轮次总结"""
        # 计算时间
        epoch_time = time.time() - self.epoch_start_time
        total_time = time.time() - self.start_time
        
        # 估计剩余时间
        avg_epoch_time = total_time / (epoch + 1)
        remaining_epochs = self.total_epochs - (epoch + 1)
        estimated_remaining = avg_epoch_time * remaining_epochs
        
        # 检查是否有改进
        improved = val_loss < self.best_val_loss
        if improved:
            self.best_val_loss = val_loss
            self.no_improve_count = 0
            improvement_indicator = "✅"
        else:
            self.no_improve_count += 1
            improvement_indicator = "❌"
        
        # 创建进度条
        progress_percent = (epoch + 1) / self.total_epochs * 100
        progress_bar_length = 50
        filled_length = int(progress_bar_length * (epoch + 1) // self.total_epochs)
        bar = '█' * filled_length + '░' * (progress_bar_length - filled_length)
        
        # 打印详细信息
        print(f"\nEpoch {epoch+1:2d}/{self.total_epochs} [Train]: {progress_percent:5.1f}%|{bar}| "
              f"{epoch+1}/{self.total_epochs} [{self._format_time(epoch_time)}<{self._format_time(estimated_remaining)}, "
              f"{1/epoch_time:.2f}it/s]")
        
        print(f"Epoch {epoch+1:2d}/{self.total_epochs} [Val]:   100%|{'█'*progress_bar_length}| "
              f"{self.val_batches}/{self.val_batches} [{self._format_time(epoch_time)}<00:00, "
              f"{self.val_batches/epoch_time:.2f}it/s]")
        
        print(f"Epoch {epoch+1:2d}/{self.total_epochs} - loss: {train_loss:.4f} - mae: {train_mae:.4f} - "
              f"val_loss: {val_loss:.4f} - val_mae: {val_mae:.4f} - lr: {learning_rate:.6f}")
        
        if improved:
            print(f"Epoch {epoch+1:2d}: val_loss improved from {self.best_val_loss:.6f} to {val_loss:.6f} {improvement_indicator}")
        else:
            print(f"Epoch {epoch+1:2d}: val_loss did not improve from {self.best_val_loss:.6f} {improvement_indicator}")
    
    def print_early_stopping(self, epoch: int, patience: int):
        """打印早停信息"""
        print(f"\n🛑 Early stopping triggered after {epoch+1} epochs (patience: {patience})")
        print(f"Best validation loss: {self.best_val_loss:.6f}")
    
    def print_training_complete(self):
        """打印训练完成信息"""
        total_time = time.time() - self.start_time
        print("\n" + "=" * 80)
        print("🎉 训练完成!")
        print("=" * 80)
        print(f"总训练时间: {self._format_time(total_time)}")
        print(f"最佳验证损失: {self.best_val_loss:.6f}")
        print(f"结束时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("=" * 80)
    
    def _format_time(self, seconds: float) -> str:
        """格式化时间显示"""
        if seconds < 60:
            return f"{seconds:.0f}s"
        elif seconds < 3600:
            minutes = int(seconds // 60)
            secs = int(seconds % 60)
            return f"{minutes:02d}:{secs:02d}"
        else:
            hours = int(seconds // 3600)
            minutes = int((seconds % 3600) // 60)
            secs = int(seconds % 60)
            return f"{hours:02d}:{minutes:02d}:{secs:02d}"


class ColoredTrainingProgress(EnhancedTrainingProgress):
    """带颜色的训练进度显示器"""
    
    # ANSI颜色代码
    COLORS = {
        'red': '\033[91m',
        'green': '\033[92m',
        'yellow': '\033[93m',
        'blue': '\033[94m',
        'magenta': '\033[95m',
        'cyan': '\033[96m',
        'white': '\033[97m',
        'bold': '\033[1m',
        'end': '\033[0m'
    }
    
    def _colorize(self, text: str, color: str) -> str:
        """给文本添加颜色"""
        return f"{self.COLORS.get(color, '')}{text}{self.COLORS['end']}"
    
    def start_training(self):
        """开始训练（带颜色）"""
        self.start_time = time.time()
        print(self._colorize("=" * 80, 'cyan'))
        print(self._colorize("🚀 开始模型训练", 'bold'))
        print(self._colorize("=" * 80, 'cyan'))
        print(f"总训练轮数: {self._colorize(str(self.total_epochs), 'yellow')}")
        print(f"训练批次数: {self._colorize(str(self.train_batches), 'yellow')}")
        print(f"验证批次数: {self._colorize(str(self.val_batches), 'yellow')}")
        print(f"开始时间: {self._colorize(datetime.now().strftime('%Y-%m-%d %H:%M:%S'), 'green')}")
        print(self._colorize("=" * 80, 'cyan'))
    
    def print_epoch_summary(self, epoch: int, train_loss: float, train_mae: float, 
                          val_loss: float, val_mae: float, learning_rate: float):
        """打印轮次总结（带颜色）"""
        # 计算时间
        epoch_time = time.time() - self.epoch_start_time
        total_time = time.time() - self.start_time
        
        # 估计剩余时间
        avg_epoch_time = total_time / (epoch + 1)
        remaining_epochs = self.total_epochs - (epoch + 1)
        estimated_remaining = avg_epoch_time * remaining_epochs
        
        # 检查是否有改进
        improved = val_loss < self.best_val_loss
        if improved:
            self.best_val_loss = val_loss
            self.no_improve_count = 0
            improvement_indicator = self._colorize("✅", 'green')
            loss_color = 'green'
        else:
            self.no_improve_count += 1
            improvement_indicator = self._colorize("❌", 'red')
            loss_color = 'red'
        
        # 创建进度条
        progress_percent = (epoch + 1) / self.total_epochs * 100
        progress_bar_length = 50
        filled_length = int(progress_bar_length * (epoch + 1) // self.total_epochs)
        bar = self._colorize('█' * filled_length, 'green') + self._colorize('░' * (progress_bar_length - filled_length), 'white')
        
        # 打印详细信息
        epoch_info = self._colorize(f"Epoch {epoch+1:2d}/{self.total_epochs}", 'blue')
        print(f"\n{epoch_info} [Train]: {self._colorize(f'{progress_percent:5.1f}%', 'yellow')}|{bar}| "
              f"{epoch+1}/{self.total_epochs} [{self._colorize(self._format_time(epoch_time), 'cyan')}<"
              f"{self._colorize(self._format_time(estimated_remaining), 'magenta')}, "
              f"{self._colorize(f'{1/epoch_time:.2f}it/s', 'white')}]")
        
        print(f"{epoch_info} [Val]:   {self._colorize('100%', 'green')}|{self._colorize('█'*progress_bar_length, 'green')}| "
              f"{self.val_batches}/{self.val_batches} [{self._colorize(self._format_time(epoch_time), 'cyan')}<00:00, "
              f"{self._colorize(f'{self.val_batches/epoch_time:.2f}it/s', 'white')}]")
        
        print(f"{epoch_info} - loss: {self._colorize(f'{train_loss:.4f}', 'yellow')} - "
              f"mae: {self._colorize(f'{train_mae:.4f}', 'yellow')} - "
              f"val_loss: {self._colorize(f'{val_loss:.4f}', loss_color)} - "
              f"val_mae: {self._colorize(f'{val_mae:.4f}', loss_color)} - "
              f"lr: {self._colorize(f'{learning_rate:.6f}', 'cyan')}")
        
        if improved:
            print(f"{epoch_info}: val_loss improved from {self._colorize(f'{self.best_val_loss:.6f}', 'yellow')} "
                  f"to {self._colorize(f'{val_loss:.6f}', 'green')} {improvement_indicator}")
        else:
            print(f"{epoch_info}: val_loss did not improve from {self._colorize(f'{self.best_val_loss:.6f}', 'yellow')} {improvement_indicator}")
    
    def print_training_complete(self):
        """打印训练完成信息（带颜色）"""
        total_time = time.time() - self.start_time
        print("\n" + self._colorize("=" * 80, 'cyan'))
        print(self._colorize("🎉 训练完成!", 'bold'))
        print(self._colorize("=" * 80, 'cyan'))
        print(f"总训练时间: {self._colorize(self._format_time(total_time), 'green')}")
        print(f"最佳验证损失: {self._colorize(f'{self.best_val_loss:.6f}', 'green')}")
        print(f"结束时间: {self._colorize(datetime.now().strftime('%Y-%m-%d %H:%M:%S'), 'green')}")
        print(self._colorize("=" * 80, 'cyan'))


def create_progress_tracker(total_epochs: int, train_batches: int, val_batches: int, 
                          use_colors: bool = True) -> EnhancedTrainingProgress:
    """
    创建进度跟踪器
    
    Args:
        total_epochs: 总训练轮数
        train_batches: 训练批次数
        val_batches: 验证批次数
        use_colors: 是否使用颜色显示
        
    Returns:
        进度跟踪器实例
    """
    if use_colors and sys.stdout.isatty():  # 检查是否支持颜色输出
        return ColoredTrainingProgress(total_epochs, train_batches, val_batches)
    else:
        return EnhancedTrainingProgress(total_epochs, train_batches, val_batches)
