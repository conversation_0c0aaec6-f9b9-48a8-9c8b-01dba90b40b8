"""
ASB-DPTAM-BiGRU模型快速超参数搜索脚本
用于快速测试和小规模超化，适合计算资源有限的情况
"""

import os
import sys
import torch
import numpy as np
import json
from datetime import datetime
from typing import Dict, Any, List
import itertools

# 添加项目根目录到系统路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.data_processing.data_loader import DataLoader
from src.data_processing.preprocessor import DataPreprocessor
from src.models.asb_dptam_bigru_model import ASBDPTAMBiGRUModel
from src.utils.metrics import ModelEvaluator
from src.utils.config import (
    MODEL_CONFIG, DATASET_CONFIG, setup_matplotlib,
    setup_training_session, get_current_paths
)


class QuickHyperparameterSearch:
    """快速超参数搜索器"""
    
    def __init__(self, search_method: str = 'random', n_trials: int = 20):
        """
        初始化快速搜索器
        
        Args:
            search_method: 搜索方法 ('random', 'grid')
            n_trials: 搜索次数
        """
        self.search_method = search_method
        self.n_trials = n_trials
        self.data_loader = None
        self.preprocessor = None
        self.data = None
        self.results = []
        self.best_params = None
        self.best_score = float('inf')
        
        # 设置matplotlib
        setup_matplotlib()
        
        # 设置搜索会话
        setup_training_session(f'Quick_Hyperparameter_Search_{search_method}')
        
        print("=" * 80)
        print("ASB-DPTAM-BiGRU模型快速超参数搜索")
        print("=" * 80)
        print(f"搜索方法: {search_method.upper()}")
        print(f"搜索次数: {n_trials}")
        print("=" * 80)
    
    def define_search_space(self) -> Dict[str, List]:
        """定义搜索空间"""
        return {
            'n_segment': [4, 6, 8],  # 分段数
            'dptam_kernel_size': [3, 5],  # DPTAM卷积核大小
            'bigru_units': [
                [32, 16], [64, 32], [96, 48], [128, 64]
            ],  # BiGRU层单元数
            'dense_units': [
                [16, 8], [32, 16], [48, 24]
            ],  # 全连接层单元数
            'dropout_rate': [0.2, 0.3, 0.4],  # Dropout率
            'learning_rate': [0.001, 0.005, 0.01],  # 学习率
            'batch_size': [32, 64]  # 批次大小
        }
    
    def generate_parameter_combinations(self) -> List[Dict[str, Any]]:
        """生成参数组合"""
        search_space = self.define_search_space()
        
        if self.search_method == 'grid':
            # 网格搜索：生成所有组合
            keys = list(search_space.keys())
            values = list(search_space.values())
            combinations = []
            
            for combination in itertools.product(*values):
                param_dict = dict(zip(keys, combination))
                param_dict['asb_adaptive_filter'] = True  # 固定为True
                combinations.append(param_dict)
            
            # 如果组合数超过n_trials，随机选择
            if len(combinations) > self.n_trials:
                np.random.seed(42)
                combinations = np.random.choice(combinations, self.n_trials, replace=False).tolist()
            
            return combinations[:self.n_trials]
        
        else:  # random search
            # 随机搜索：随机生成组合
            np.random.seed(42)
            combinations = []
            
            for _ in range(self.n_trials):
                param_dict = {}
                for key, values in search_space.items():
                    param_dict[key] = np.random.choice(values)
                param_dict['asb_adaptive_filter'] = True  # 固定为True
                combinations.append(param_dict)
            
            return combinations
    
    def load_and_prepare_data(self) -> Dict[str, Any]:
        """加载和预处理数据"""
        print("\n" + "=" * 60)
        print("步骤1: 数据加载与预处理")
        print("=" * 60)
        
        # 数据加载
        self.data_loader = DataLoader(
            data_path=DATASET_CONFIG['data_path'],
            target_column=DATASET_CONFIG['target_column']
        )
        
        # 加载数据
        df = self.data_loader.load_data()
        print(f"数据形状: {df.shape}")
        
        # 数据预处理
        self.preprocessor = DataPreprocessor(
            sequence_length=DATASET_CONFIG['sequence_length'],
            test_size=DATASET_CONFIG['test_size'],
            val_size=DATASET_CONFIG['val_size'],
            random_state=DATASET_CONFIG['random_state']
        )
        
        # 预处理数据
        processed_data = self.preprocessor.preprocess_data(df)
        
        # 创建数据加载器
        train_loader, val_loader, test_loader = self.preprocessor.create_data_loaders(
            processed_data,
            batch_size=MODEL_CONFIG['batch_size']
        )
        
        # 准备数据字典
        self.data = {
            'train_loader': train_loader,
            'val_loader': val_loader,
            'test_loader': test_loader,
            'X_train': processed_data['X_train'],
            'y_train': processed_data['y_train'],
            'X_val': processed_data['X_val'],
            'y_val': processed_data['y_val'],
            'X_test': processed_data['X_test'],
            'y_test': processed_data['y_test'],
            'sequence_length': processed_data['sequence_length'],
            'n_features': processed_data['n_features']
        }
        
        print("数据准备完成！")
        return self.data
    
    def evaluate_parameters(self, params: Dict[str, Any], trial_idx: int) -> float:
        """评估单组参数"""
        try:
            print(f"\n试验 {trial_idx + 1}/{self.n_trials}: 测试参数组合")
            print(f"参数: {params}")
            
            # 检查序列长度是否能被分段数整除
            if self.data['sequence_length'] % params['n_segment'] != 0:
                print(f"序列长度{self.data['sequence_length']}不能被分段数{params['n_segment']}整除，跳过此试验")
                return float('inf')
            
            # 重新创建数据加载器（如果批次大小改变）
            if params['batch_size'] != MODEL_CONFIG['batch_size']:
                train_loader, val_loader, test_loader = self.preprocessor.create_data_loaders(
                    {
                        'X_train': self.data['X_train'],
                        'y_train': self.data['y_train'],
                        'X_val': self.data['X_val'],
                        'y_val': self.data['y_val'],
                        'X_test': self.data['X_test'],
                        'y_test': self.data['y_test']
                    },
                    batch_size=params['batch_size']
                )
            else:
                train_loader = self.data['train_loader']
                val_loader = self.data['val_loader']
            
            # 创建模型
            model = ASBDPTAMBiGRUModel(
                sequence_length=self.data['sequence_length'],
                n_features=self.data['n_features'],
                n_segment=params['n_segment'],
                dptam_kernel_size=params['dptam_kernel_size'],
                bigru_units=params['bigru_units'],
                dense_units=params['dense_units'],
                dropout_rate=params['dropout_rate'],
                asb_adaptive_filter=params['asb_adaptive_filter']
            )
            
            # 设置标准化器
            model.set_scalers(
                self.preprocessor.scaler_X,
                self.preprocessor.scaler_y
            )
            
            # 训练模型（使用较少的epochs进行快速评估）
            history = model.train_model(
                train_loader=train_loader,
                val_loader=val_loader,
                epochs=30,  # 快速训练
                patience=8,  # 较小的patience
                learning_rate=params['learning_rate'],
                verbose=0  # 减少输出
            )
            
            # 评估模型
            results = model.evaluate(
                X_train=self.data['X_train'],
                y_train=self.data['y_train'],
                X_val=self.data['X_val'],
                y_val=self.data['y_val'],
                X_test=self.data['X_test'],
                y_test=self.data['y_test'],
                use_normalized_metrics=True,
                verbose=0
            )
            
            # 返回验证集RMSE
            val_rmse = results['val']['RMSE']
            print(f"验证集RMSE: {val_rmse:.4f}")
            
            # 记录结果
            self.results.append({
                'trial': trial_idx + 1,
                'params': params.copy(),
                'val_rmse': val_rmse,
                'val_r2': results['val']['R2'],
                'val_mae': results['val']['MAE']
            })
            
            # 更新最佳结果
            if val_rmse < self.best_score:
                self.best_score = val_rmse
                self.best_params = params.copy()
                print(f"🎉 发现更好的参数组合！验证集RMSE: {val_rmse:.4f}")
            
            return val_rmse
            
        except Exception as e:
            print(f"试验 {trial_idx + 1} 失败: {str(e)}")
            return float('inf')
    
    def run_search(self) -> Dict[str, Any]:
        """运行搜索"""
        print("\n" + "=" * 60)
        print("步骤2: 开始超参数搜索")
        print("=" * 60)
        
        # 生成参数组合
        param_combinations = self.generate_parameter_combinations()
        print(f"生成了 {len(param_combinations)} 个参数组合")
        
        # 评估每组参数
        for i, params in enumerate(param_combinations):
            self.evaluate_parameters(params, i)
        
        print(f"\n搜索完成！")
        print(f"最佳验证集RMSE: {self.best_score:.4f}")
        print(f"最佳参数组合:")
        for key, value in self.best_params.items():
            print(f"  {key}: {value}")
        
        return {
            'best_params': self.best_params,
            'best_score': self.best_score,
            'all_results': self.results
        }
    
    def save_results(self) -> None:
        """保存搜索结果"""
        print("\n" + "=" * 60)
        print("步骤3: 保存搜索结果")
        print("=" * 60)
        
        # 创建结果目录
        results_dir = os.path.join(get_current_paths()['results_root'], 'quick_hyperparameter_search')
        os.makedirs(results_dir, exist_ok=True)
        
        # 保存结果
        results_file = os.path.join(results_dir, f'{self.search_method}_search_results.json')
        with open(results_file, 'w', encoding='utf-8') as f:
            json.dump({
                'search_method': self.search_method,
                'n_trials': self.n_trials,
                'best_params': self.best_params,
                'best_score': self.best_score,
                'all_results': self.results,
                'search_time': datetime.now().isoformat()
            }, f, indent=2, ensure_ascii=False)
        
        print(f"搜索结果已保存到: {results_file}")
    
    def run_complete_search(self) -> Dict[str, Any]:
        """运行完整搜索流程"""
        try:
            # 步骤1: 数据准备
            self.load_and_prepare_data()
            
            # 步骤2: 超参数搜索
            search_results = self.run_search()
            
            # 步骤3: 保存结果
            self.save_results()
            
            print(f"\n✅ 快速超参数搜索完成！")
            print(f"📁 结果保存路径: {get_current_paths()['results_root']}/quick_hyperparameter_search")
            print(f"🏆 最佳验证集RMSE: {self.best_score:.4f}")
            
            return search_results
            
        except Exception as e:
            print(f"\n❌ 搜索过程中发生错误: {str(e)}")
            import traceback
            traceback.print_exc()
            raise


def main():
    """主函数"""
    # 可以选择搜索方法：'random' 或 'grid'
    search_method = 'random'  # 推荐使用random，更高效
    n_trials = 20  # 可以根据计算资源调整
    
    # 创建快速搜索器
    searcher = QuickHyperparameterSearch(
        search_method=search_method,
        n_trials=n_trials
    )
    
    # 运行搜索
    results = searcher.run_complete_search()
    
    # 打印结果摘要
    print("\n" + "=" * 80)
    print("搜索结果摘要")
    print("=" * 80)
    print(f"搜索方法: {search_method.upper()}")
    print(f"搜索次数: {n_trials}")
    print(f"最佳验证集RMSE: {results['best_score']:.4f}")
    print("\n最佳超参数:")
    for key, value in results['best_params'].items():
        print(f"  {key}: {value}")
    print("=" * 80)


if __name__ == "__main__":
    main()
